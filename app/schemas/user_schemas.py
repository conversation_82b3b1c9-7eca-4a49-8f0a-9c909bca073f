"""
用户相关的数据模型
"""
from pydantic import BaseModel, Field
from typing import Optional

class UserInfoResponse(BaseModel):
    """用户信息响应模型"""
    姓名: str = Field(..., description="用户姓名")
    手机号: str = Field(..., description="手机号码")
    身份证号: str = Field(..., description="身份证号")
    企业名称: str = Field(..., description="企业名称")
    社会编码: str = Field(..., description="社会统一信用代码")
    银行卡号: str = Field(..., description="银行卡号")

class MockUserRequest(BaseModel):
    """模拟用户请求模型"""
    user_type: str = Field(..., description="用户类型: personal/enterprise")
    count: int = Field(1, description="生成数量", ge=1, le=10)

class MockUserResponse(BaseModel):
    """模拟用户响应模型"""
    status: str = Field(..., description="状态")
    data: dict = Field(..., description="用户数据")
    message: Optional[str] = Field(None, description="消息")
