"""
证书相关的数据模型
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import IntEnum

class UserType(IntEnum):
    """用户类型枚举"""
    PERSONAL = 1  # 个人
    ENTERPRISE = 2  # 企业

class ProductType(IntEnum):
    """产品类型枚举"""
    TIANYIN = 1
    TCLOUD = 2
    ESHIELD = 3

class CertificateTaskRequest(BaseModel):
    """证书任务请求模型"""
    user_type: UserType = Field(..., description="用户类型: 1-个人, 2-企业")
    oid: Optional[str] = Field(None, description="如果传入oid，则不使用姓名和证件号")
    products: List[ProductType] = Field([ProductType.TIANYIN], description="产品列表")
    use_old_data: int = Field(1, description="是否使用老数据: 0-不使用, 1-使用")

class CertificateTaskResponse(BaseModel):
    """证书任务响应模型"""
    status: str = Field(..., description="状态")
    task_id: str = Field(..., description="任务ID")
    message: str = Field(..., description="消息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")

class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""
    status: str = Field(..., description="任务状态")
    task_id: str = Field(..., description="任务ID")
    details: str = Field(..., description="任务详情")
    progress: int = Field(..., description="进度百分比", ge=0, le=100)
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")

class TaskListResponse(BaseModel):
    """任务列表响应模型"""
    total: int = Field(..., description="总数量")
    items: List[TaskStatusResponse] = Field(..., description="任务列表")
    limit: int = Field(..., description="限制数量")
    offset: int = Field(..., description="偏移量")
