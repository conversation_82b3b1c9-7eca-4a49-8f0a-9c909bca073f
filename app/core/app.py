"""
应用核心配置和创建
"""
from fastapi import FastAPI
from fastapi_mcp import FastApiMCP
import logging

from app.api.routes import api_router
from app.core.config import settings

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

def create_app() -> FastAPI:
    """
    创建FastAPI应用实例
    """
    app = FastAPI(
        title=settings.PROJECT_NAME,
        description=settings.PROJECT_DESCRIPTION,
        version=settings.VERSION,
    )
    
    # 包含API路由
    app.include_router(api_router, prefix="/api")
    
    # 创建MCP服务器
    mcp = FastApiMCP(
        app,
        name=settings.MCP_NAME,
        description=settings.MCP_DESCRIPTION,
        describe_all_responses=True,
        describe_full_response_schema=True
    )
    
    # 挂载MCP服务器
    mcp.mount()
    
    return app
