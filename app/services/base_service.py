"""
基础服务类
"""
import logging
from typing import Dict, Any
import requests
from app.core.config import settings

class BaseService:
    """基础服务类，提供通用功能"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.base_url = settings.EXTERNAL_API_BASE_URL
    
    async def make_request(
        self, 
        method: str, 
        url: str, 
        headers: Dict[str, str] = None, 
        data: Dict[str, Any] = None,
        json_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        发送HTTP请求的通用方法
        
        :param method: HTTP方法
        :param url: 请求URL
        :param headers: 请求头
        :param data: 表单数据
        :param json_data: JSON数据
        :return: 响应数据
        """
        try:
            self.logger.info(f"发送{method}请求到: {url}")
            
            if method.upper() == "GET":
                response = requests.get(url, headers=headers, params=data)
            elif method.upper() == "POST":
                if json_data:
                    response = requests.post(url, headers=headers, json=json_data)
                else:
                    response = requests.post(url, headers=headers, data=data)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求失败: {str(e)}")
            raise Exception(f"请求失败: {str(e)}")
        except Exception as e:
            self.logger.error(f"处理请求时发生错误: {str(e)}")
            raise
