from fastapi import FastAPI
import uvicorn
from fastapi_mcp import FastApiMCP
import requests
# Create (or import) a FastAPI app
app = FastAPI()



from fastapi import APIRouter, Body
from typing import Any, Optional, List
from pydantic import BaseModel
import sqlite3
import logging
import os

# 创建路由实例
router = APIRouter()

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)





# 用户相关造数接口


# @app.get("/users/{user_id}", operation_id="get_user_info")
# async def read_user(user_id: int):
#     return {"user_id": user_id}
@app.get("/users/{env}", operation_id="get_user_or_enterprise_info",description="获取用户或企业信息")
async def read_user(env:  int):
    """
    :param env: 枚举值：1、测试环境 2、模拟环境
    :return:
    """


    url = "http://sdk.testk8s.tsign.cn/random/get"
    method = "POST"
    headers = {}
    data = {
        "env": "模拟环境" if env == 1 else "测试环境",
        "mock": True,
        "total": 1
    }

    # 使用 请求接口 方法发送请求
    response = requests.post(url, headers=headers, json=data)
    response = response.json()

    accountData = response["accountList"][0]
    accountDataNew = {}
    accountDataNew["姓名"] = accountData["name"]
    accountDataNew["手机号"] = accountData["phone"]
    accountDataNew["身份证号"] = accountData["idNo"]
    accountDataNew["企业名称"] = accountData["orgName"]
    accountDataNew["社会编码"] = accountData["orgCode"]
    accountDataNew["银行卡号"] = accountData["bankCard"]
    return accountDataNew




app.include_router(router)
# Create an MCP server based on this app
mcp = FastApiMCP(app,
                 name="My API MCP",
                 description="Very cool MCP server",
                 describe_all_responses=True,
                 describe_full_response_schema=True
                 )

# Mount the MCP server directly to your app
mcp.mount()
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)